#!/bin/bash

echo "========================================"
echo "CMake Build Script for Transformation"
echo "========================================"

# 函数：显示帮助信息
show_help() {
    echo "Usage: $0 [options]"
    echo "Options:"
    echo "  clean    - Clean previous build files"
    echo "  help     - Show this help message"
    echo "  run      - Build and run the program"
    echo ""
    echo "Examples:"
    echo "  $0           # Just build"
    echo "  $0 clean    # Clean and build"
    echo "  $0 run      # Build and run"
}

# 检查参数
CLEAN_BUILD=false
RUN_PROGRAM=false

for arg in "$@"; do
    case $arg in
        clean)
            CLEAN_BUILD=true
            ;;
        run)
            RUN_PROGRAM=true
            ;;
        help)
            show_help
            exit 0
            ;;
        *)
            echo "Unknown option: $arg"
            show_help
            exit 1
            ;;
    esac
done

# 创建 build 目录（如果不存在）
if [ ! -d "build" ]; then
    echo "Creating build directory..."
    mkdir build
fi

# 进入 build 目录
cd build

# 清理之前的构建文件（如果需要）
if [ "$CLEAN_BUILD" = true ]; then
    echo "Cleaning previous build files..."
    rm -rf *
fi

# 运行 CMake 配置
echo "Running CMake configuration..."
cmake .. -G "MinGW Makefiles"
if [ $? -ne 0 ]; then
    echo "CMake configuration failed!"
    exit 1
fi

# 编译项目
echo "Building project..."
cmake --build .
if [ $? -ne 0 ]; then
    echo "Build failed!"
    exit 1
fi

echo "========================================"
echo "Build completed successfully!"
echo "Executable: build/Transformation.exe"
echo "========================================"

# 运行程序（如果需要）
if [ "$RUN_PROGRAM" = true ]; then
    echo "Running Transformation.exe..."
    echo "----------------------------------------"
    ./Transformation.exe
    echo "----------------------------------------"
fi
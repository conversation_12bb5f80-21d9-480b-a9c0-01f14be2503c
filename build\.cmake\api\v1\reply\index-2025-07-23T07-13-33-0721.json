{"cmake": {"generator": {"multiConfig": false, "name": "MinGW Makefiles"}, "paths": {"cmake": "E:/Cmake/bin/cmake.exe", "cpack": "E:/Cmake/bin/cpack.exe", "ctest": "E:/Cmake/bin/ctest.exe", "root": "E:/Cmake/share/cmake-3.30"}, "version": {"isDirty": false, "major": 3, "minor": 30, "patch": 0, "string": "3.30.0-rc1", "suffix": "rc1"}}, "objects": [{"jsonFile": "codemodel-v2-a6af633a2acaf6d04cc0.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "cache-v2-9342ba8db6f91e3e5772.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-6a500115ab11ce4a6d84.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}, {"jsonFile": "toolchains-v1-8aa00bd977b5f6a692db.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}], "reply": {"client-vscode": {"query.json": {"requests": [{"kind": "cache", "version": 2}, {"kind": "codemodel", "version": 2}, {"kind": "toolchains", "version": 1}, {"kind": "cmakeFiles", "version": 1}], "responses": [{"jsonFile": "cache-v2-9342ba8db6f91e3e5772.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "codemodel-v2-a6af633a2acaf6d04cc0.json", "kind": "codemodel", "version": {"major": 2, "minor": 7}}, {"jsonFile": "toolchains-v1-8aa00bd977b5f6a692db.json", "kind": "toolchains", "version": {"major": 1, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-6a500115ab11ce4a6d84.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 1}}]}}}}
{"artifacts": [{"path": "Transformation.exe"}, {"path": "Transformation.pdb"}], "backtrace": 1, "backtraceGraph": {"commands": ["add_executable", "include_directories"], "files": ["CMakeLists.txt"], "nodes": [{"file": 0}, {"command": 0, "file": 0, "line": 12, "parent": 0}, {"command": 1, "file": 0, "line": 10, "parent": 0}]}, "compileGroups": [{"compileCommandFragments": [{"fragment": "-g -std=gnu++14"}], "includes": [{"backtrace": 2, "path": "E:/eigen-3.4.0"}], "language": "CXX", "languageStandard": {"backtraces": [1], "standard": "14"}, "sourceIndexes": [0]}], "id": "Transformation::@6890427a1f51a3e7e1df", "link": {"commandFragments": [{"fragment": "-g", "role": "flags"}, {"fragment": "", "role": "flags"}, {"fragment": "-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32", "role": "libraries"}], "language": "CXX"}, "name": "Transformation", "nameOnDisk": "Transformation.exe", "paths": {"build": ".", "source": "."}, "sourceGroups": [{"name": "Source Files", "sourceIndexes": [0]}], "sources": [{"backtrace": 1, "compileGroupIndex": 0, "path": "main.cpp", "sourceGroupIndex": 0}], "type": "EXECUTABLE"}
{"kind": "toolchains", "toolchains": [{"compiler": {"id": "GNU", "implicit": {"includeDirectories": ["E:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include", "E:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed", "E:/mingw64/x86_64-w64-mingw32/include"], "linkDirectories": ["E:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0", "E:/mingw64/lib/gcc", "E:/mingw64/x86_64-w64-mingw32/lib", "E:/mingw64/lib"], "linkFrameworkDirectories": [], "linkLibraries": ["mingw32", "gcc", "moldname", "mingwex", "advapi32", "shell32", "user32", "kernel32", "iconv", "mingw32", "gcc", "moldname", "mingwex"]}, "path": "E:/mingw64/bin/gcc.exe", "version": "8.1.0"}, "language": "C", "sourceFileExtensions": ["c", "m"]}, {"compiler": {"id": "GNU", "implicit": {"includeDirectories": ["E:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++", "E:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/x86_64-w64-mingw32", "E:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include/c++/backward", "E:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include", "E:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0/include-fixed", "E:/mingw64/x86_64-w64-mingw32/include"], "linkDirectories": ["E:/mingw64/lib/gcc/x86_64-w64-mingw32/8.1.0", "E:/mingw64/lib/gcc", "E:/mingw64/x86_64-w64-mingw32/lib", "E:/mingw64/lib"], "linkFrameworkDirectories": [], "linkLibraries": ["stdc++", "mingw32", "gcc_s", "gcc", "moldname", "mingwex", "advapi32", "shell32", "user32", "kernel32", "iconv", "mingw32", "gcc_s", "gcc", "moldname", "mingwex"]}, "path": "E:/mingw64/bin/g++.exe", "version": "8.1.0"}, "language": "CXX", "sourceFileExtensions": ["C", "M", "c++", "cc", "cpp", "cxx", "mm", "mpp", "CPP", "ixx", "cppm", "ccm", "cxxm", "c++m"]}, {"compiler": {"implicit": {}, "path": "E:/mingw64/bin/windres.exe"}, "language": "RC", "sourceFileExtensions": ["rc", "RC"]}], "version": {"major": 1, "minor": 0}}